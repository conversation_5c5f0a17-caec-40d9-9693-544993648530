globalThis.makoModuleHotUpdate('src/pages/team-management/index.tsx', {
    modules: {
        "src/pages/team-management/components/TeamInvitationList.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _dayjs = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/dayjs/dayjs.min.js"));
            var _services = __mako_require__("src/services/index.ts");
            var _api = __mako_require__("src/types/api.ts");
            var _InvitationStatus = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/components/InvitationStatus.tsx"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Text } = _antd.Typography;
            const { Option } = _antd.Select;
            const TeamInvitationList = ({ onRefresh })=>{
                _s();
                const [invitations, setInvitations] = (0, _react.useState)([]);
                const [loading, setLoading] = (0, _react.useState)(false);
                const [searchText, setSearchText] = (0, _react.useState)('');
                const [statusFilter, setStatusFilter] = (0, _react.useState)('');
                // 获取邀请列表
                const fetchInvitations = async ()=>{
                    try {
                        setLoading(true);
                        const invitationList = await _services.InvitationService.getCurrentTeamInvitations();
                        setInvitations(invitationList || []);
                    } catch (error) {
                        console.error('获取邀请列表失败:', error);
                        _antd.message.error('获取邀请列表失败');
                        setInvitations([]);
                    } finally{
                        setLoading(false);
                    }
                };
                (0, _react.useEffect)(()=>{
                    fetchInvitations();
                }, []);
                // 取消邀请
                const handleCancelInvitation = async (invitationId)=>{
                    try {
                        await _services.InvitationService.cancelInvitation(invitationId);
                        _antd.message.success('邀请取消成功');
                        fetchInvitations();
                        onRefresh === null || onRefresh === void 0 || onRefresh();
                    } catch (error) {
                        console.error('取消邀请失败:', error);
                        _antd.message.error('取消邀请失败');
                    }
                };
                // 过滤邀请列表
                const filteredInvitations = invitations.filter((invitation)=>{
                    const matchesSearch = !searchText || invitation.inviteeEmail.toLowerCase().includes(searchText.toLowerCase()) || invitation.inviteeName && invitation.inviteeName.toLowerCase().includes(searchText.toLowerCase());
                    const matchesStatus = !statusFilter || invitation.status === statusFilter;
                    return matchesSearch && matchesStatus;
                });
                // 表格列定义
                const columns = [
                    {
                        title: '被邀请人',
                        key: 'invitee',
                        render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                direction: "vertical",
                                size: 0,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        children: record.inviteeName || '未注册用户'
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                        lineNumber: 105,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        style: {
                                            fontSize: '12px'
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                                lineNumber: 107,
                                                columnNumber: 13
                                            }, this),
                                            " ",
                                            record.inviteeEmail
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                        lineNumber: 106,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                lineNumber: 104,
                                columnNumber: 9
                            }, this)
                    },
                    {
                        title: '邀请状态',
                        dataIndex: 'status',
                        key: 'status',
                        render: (status, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_InvitationStatus.default, {
                                status: status,
                                isExpired: record.isExpired
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                lineNumber: 117,
                                columnNumber: 9
                            }, this),
                        filters: [
                            {
                                text: '待确认',
                                value: _api.InvitationStatus.PENDING
                            },
                            {
                                text: '已接受',
                                value: _api.InvitationStatus.ACCEPTED
                            },
                            {
                                text: '已拒绝',
                                value: _api.InvitationStatus.REJECTED
                            },
                            {
                                text: '已过期',
                                value: _api.InvitationStatus.EXPIRED
                            },
                            {
                                text: '已取消',
                                value: _api.InvitationStatus.CANCELLED
                            }
                        ],
                        onFilter: (value, record)=>record.status === value
                    },
                    {
                        title: '邀请时间',
                        dataIndex: 'invitedAt',
                        key: 'invitedAt',
                        render: (time)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                title: (0, _dayjs.default)(time).format('YYYY-MM-DD HH:mm:ss'),
                                children: (0, _dayjs.default)(time).format('MM-DD HH:mm')
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                lineNumber: 136,
                                columnNumber: 9
                            }, this),
                        sorter: (a, b)=>(0, _dayjs.default)(a.invitedAt).unix() - (0, _dayjs.default)(b.invitedAt).unix()
                    },
                    {
                        title: '过期时间',
                        dataIndex: 'expiresAt',
                        key: 'expiresAt',
                        render: (time, record)=>{
                            const isExpired = record.isExpired;
                            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                title: (0, _dayjs.default)(time).format('YYYY-MM-DD HH:mm:ss'),
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: isExpired ? 'danger' : 'secondary',
                                    children: (0, _dayjs.default)(time).format('MM-DD HH:mm')
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                    lineNumber: 150,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                lineNumber: 149,
                                columnNumber: 11
                            }, this);
                        }
                    },
                    {
                        title: '响应时间',
                        dataIndex: 'respondedAt',
                        key: 'respondedAt',
                        render: (time)=>time ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                title: (0, _dayjs.default)(time).format('YYYY-MM-DD HH:mm:ss'),
                                children: (0, _dayjs.default)(time).format('MM-DD HH:mm')
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                lineNumber: 162,
                                columnNumber: 9
                            }, this) : '-'
                    },
                    {
                        title: '邀请链接',
                        key: 'invitationLink',
                        render: (_, record)=>{
                            if (!record.invitationLink) return '-';
                            const copyLink = ()=>{
                                navigator.clipboard.writeText(record.invitationLink).then(()=>{
                                    _antd.message.success('邀请链接已复制到剪贴板');
                                }).catch(()=>{
                                    _antd.message.error('复制失败，请手动复制');
                                });
                            };
                            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                        title: "复制邀请链接",
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            size: "small",
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CopyOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                                lineNumber: 186,
                                                columnNumber: 23
                                            }, void 0),
                                            onClick: copyLink,
                                            children: "复制链接"
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                            lineNumber: 184,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                        lineNumber: 183,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                        title: record.invitationLink,
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            size: "small",
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.LinkOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                                lineNumber: 195,
                                                columnNumber: 23
                                            }, void 0),
                                            onClick: ()=>window.open(record.invitationLink, '_blank'),
                                            children: "预览"
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                            lineNumber: 193,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                        lineNumber: 192,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                lineNumber: 182,
                                columnNumber: 11
                            }, this);
                        }
                    },
                    {
                        title: '操作',
                        key: 'action',
                        render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: record.canBeCancelled && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                                    title: "确定要取消这个邀请吗？",
                                    description: "取消后被邀请人将无法通过此邀请加入团队",
                                    onConfirm: ()=>handleCancelInvitation(record.id),
                                    okText: "确定",
                                    cancelText: "取消",
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        size: "small",
                                        danger: true,
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                            lineNumber: 221,
                                            columnNumber: 23
                                        }, void 0),
                                        children: "取消邀请"
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                        lineNumber: 218,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                    lineNumber: 211,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                lineNumber: 209,
                                columnNumber: 9
                            }, this)
                    }
                ];
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                    title: "邀请记录",
                    extra: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ReloadOutlined, {}, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                lineNumber: 238,
                                columnNumber: 19
                            }, void 0),
                            onClick: fetchInvitations,
                            loading: loading,
                            children: "刷新"
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                            lineNumber: 237,
                            columnNumber: 11
                        }, void 0)
                    }, void 0, false, {
                        fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                        lineNumber: 236,
                        columnNumber: 9
                    }, void 0),
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                            style: {
                                marginBottom: 16
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                    placeholder: "搜索邮箱或姓名",
                                    prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                        lineNumber: 251,
                                        columnNumber: 19
                                    }, void 0),
                                    value: searchText,
                                    onChange: (e)=>setSearchText(e.target.value),
                                    style: {
                                        width: 200
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                    lineNumber: 249,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                                    placeholder: "筛选状态",
                                    value: statusFilter,
                                    onChange: setStatusFilter,
                                    style: {
                                        width: 120
                                    },
                                    allowClear: true,
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                                            value: _api.InvitationStatus.PENDING,
                                            children: "待确认"
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                            lineNumber: 263,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                                            value: _api.InvitationStatus.ACCEPTED,
                                            children: "已接受"
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                            lineNumber: 264,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                                            value: _api.InvitationStatus.REJECTED,
                                            children: "已拒绝"
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                            lineNumber: 265,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                                            value: _api.InvitationStatus.EXPIRED,
                                            children: "已过期"
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                            lineNumber: 266,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Option, {
                                            value: _api.InvitationStatus.CANCELLED,
                                            children: "已取消"
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                            lineNumber: 267,
                                            columnNumber: 11
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                                    lineNumber: 256,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                            lineNumber: 248,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Table, {
                            columns: columns,
                            dataSource: filteredInvitations,
                            rowKey: "id",
                            loading: loading,
                            pagination: {
                                showSizeChanger: true,
                                showQuickJumper: true,
                                showTotal: (total)=>`共 ${total} 条邀请记录`,
                                pageSize: 10
                            }
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                            lineNumber: 272,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team-management/components/TeamInvitationList.tsx",
                    lineNumber: 233,
                    columnNumber: 5
                }, this);
            };
            _s(TeamInvitationList, "Sx7tbJXUjergP+RFG/GYyQ2fSfU=");
            _c = TeamInvitationList;
            var _default = TeamInvitationList;
            var _c;
            $RefreshReg$(_c, "TeamInvitationList");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '6961211749660672507';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "common",
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/invitations/index.tsx": [
            "common",
            "p__user__invitations__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ],
        "src/pages/user/team-select/index.tsx": [
            "common",
            "src/pages/user/team-select/index.tsx"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_team-management_index_tsx-async.10686901486861475193.hot-update.js.map