{"version": 3, "sources": ["src_pages_team-management_index_tsx-async.8347278114515712687.hot-update.js", "src/pages/team-management/components/TeamInvitationList.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/team-management/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='8332297343527100661';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"common\",\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/invitations/index.tsx\":[\"common\",\"p__user__invitations__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"],\"src/pages/user/team-select/index.tsx\":[\"common\",\"src/pages/user/team-select/index.tsx\"]});;\r\n  },\r\n);\r\n", "/**\n * 团队邀请列表组件\n * \n * 功能特性：\n * - 显示团队发出的所有邀请记录\n * - 支持取消待处理的邀请\n * - 显示邀请状态和过期时间\n * - 支持搜索和筛选\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  message,\n  Popconfirm,\n  Typography,\n  Tag,\n  Tooltip,\n} from 'antd';\nimport {\n  SearchOutlined,\n  DeleteOutlined,\n  ReloadOutlined,\n  MailOutlined,\n  LinkOutlined,\n  CopyOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport dayjs from 'dayjs';\n\n// 导入服务和类型\nimport { InvitationService } from '@/services';\nimport type { TeamInvitationResponse } from '@/types/api';\nimport { InvitationStatus } from '@/types/api';\nimport InvitationStatusComponent from '@/components/InvitationStatus';\n\nconst { Text } = Typography;\nconst { Option } = Select;\n\ninterface TeamInvitationListProps {\n  onRefresh?: () => void;\n}\n\nconst TeamInvitationList: React.FC<TeamInvitationListProps> = ({ onRefresh }) => {\n  const [invitations, setInvitations] = useState<TeamInvitationResponse[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [searchText, setSearchText] = useState('');\n  const [statusFilter, setStatusFilter] = useState<string>('');\n\n  // 获取邀请列表\n  const fetchInvitations = async () => {\n    try {\n      setLoading(true);\n      const invitationList = await InvitationService.getCurrentTeamInvitations();\n      setInvitations(invitationList || []);\n    } catch (error) {\n      console.error('获取邀请列表失败:', error);\n      message.error('获取邀请列表失败');\n      setInvitations([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchInvitations();\n  }, []);\n\n  // 取消邀请\n  const handleCancelInvitation = async (invitationId: number) => {\n    try {\n      await InvitationService.cancelInvitation(invitationId);\n      message.success('邀请取消成功');\n      fetchInvitations();\n      // 移除 onRefresh() 调用，避免整个页面刷新\n      // onRefresh?.();\n    } catch (error) {\n      console.error('取消邀请失败:', error);\n      message.error('取消邀请失败');\n    }\n  };\n\n  // 过滤邀请列表\n  const filteredInvitations = invitations.filter(invitation => {\n    const matchesSearch = !searchText || \n      invitation.inviteeEmail.toLowerCase().includes(searchText.toLowerCase()) ||\n      (invitation.inviteeName && invitation.inviteeName.toLowerCase().includes(searchText.toLowerCase()));\n    \n    const matchesStatus = !statusFilter || invitation.status === statusFilter;\n    \n    return matchesSearch && matchesStatus;\n  });\n\n  // 表格列定义\n  const columns: ColumnsType<TeamInvitationResponse> = [\n    {\n      title: '被邀请人',\n      key: 'invitee',\n      render: (_, record) => (\n        <Space direction=\"vertical\" size={0}>\n          <Text strong>{record.inviteeName || '未注册用户'}</Text>\n          <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n            <MailOutlined /> {record.inviteeEmail}\n          </Text>\n        </Space>\n      ),\n    },\n    {\n      title: '邀请状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status, record) => (\n        <InvitationStatusComponent \n          status={status} \n          isExpired={record.isExpired} \n        />\n      ),\n      filters: [\n        { text: '待确认', value: InvitationStatus.PENDING },\n        { text: '已接受', value: InvitationStatus.ACCEPTED },\n        { text: '已拒绝', value: InvitationStatus.REJECTED },\n        { text: '已过期', value: InvitationStatus.EXPIRED },\n        { text: '已取消', value: InvitationStatus.CANCELLED },\n      ],\n      onFilter: (value, record) => record.status === value,\n    },\n    {\n      title: '邀请时间',\n      dataIndex: 'invitedAt',\n      key: 'invitedAt',\n      render: (time) => (\n        <Tooltip title={dayjs(time).format('YYYY-MM-DD HH:mm:ss')}>\n          {dayjs(time).format('MM-DD HH:mm')}\n        </Tooltip>\n      ),\n      sorter: (a, b) => dayjs(a.invitedAt).unix() - dayjs(b.invitedAt).unix(),\n    },\n    {\n      title: '过期时间',\n      dataIndex: 'expiresAt',\n      key: 'expiresAt',\n      render: (time, record) => {\n        const isExpired = record.isExpired;\n        return (\n          <Tooltip title={dayjs(time).format('YYYY-MM-DD HH:mm:ss')}>\n            <Text type={isExpired ? 'danger' : 'secondary'}>\n              {dayjs(time).format('MM-DD HH:mm')}\n            </Text>\n          </Tooltip>\n        );\n      },\n    },\n    {\n      title: '响应时间',\n      dataIndex: 'respondedAt',\n      key: 'respondedAt',\n      render: (time) => time ? (\n        <Tooltip title={dayjs(time).format('YYYY-MM-DD HH:mm:ss')}>\n          {dayjs(time).format('MM-DD HH:mm')}\n        </Tooltip>\n      ) : '-',\n    },\n    {\n      title: '邀请链接',\n      key: 'invitationLink',\n      render: (_, record) => {\n        if (!record.invitationLink) return '-';\n\n        const copyLink = () => {\n          navigator.clipboard.writeText(record.invitationLink!).then(() => {\n            message.success('邀请链接已复制到剪贴板');\n          }).catch(() => {\n            message.error('复制失败，请手动复制');\n          });\n        };\n\n        return (\n          <Space>\n            <Tooltip title=\"复制邀请链接\">\n              <Button\n                size=\"small\"\n                icon={<CopyOutlined />}\n                onClick={copyLink}\n              >\n                复制链接\n              </Button>\n            </Tooltip>\n            <Tooltip title={record.invitationLink}>\n              <Button\n                size=\"small\"\n                icon={<LinkOutlined />}\n                onClick={() => window.open(record.invitationLink, '_blank')}\n              >\n                预览\n              </Button>\n            </Tooltip>\n          </Space>\n        );\n      },\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <Space>\n          {record.canBeCancelled && (\n            <Popconfirm\n              title=\"确定要取消这个邀请吗？\"\n              description=\"取消后被邀请人将无法通过此邀请加入团队\"\n              onConfirm={() => handleCancelInvitation(record.id)}\n              okText=\"确定\"\n              cancelText=\"取消\"\n            >\n              <Button\n                size=\"small\"\n                danger\n                icon={<DeleteOutlined />}\n              >\n                取消邀请\n              </Button>\n            </Popconfirm>\n          )}\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <Card\n      title=\"邀请记录\"\n      extra={\n        <Space>\n          <Button \n            icon={<ReloadOutlined />} \n            onClick={fetchInvitations}\n            loading={loading}\n          >\n            刷新\n          </Button>\n        </Space>\n      }\n    >\n      {/* 搜索和筛选 */}\n      <Space style={{ marginBottom: 16 }}>\n        <Input\n          placeholder=\"搜索邮箱或姓名\"\n          prefix={<SearchOutlined />}\n          value={searchText}\n          onChange={(e) => setSearchText(e.target.value)}\n          style={{ width: 200 }}\n        />\n        <Select\n          placeholder=\"筛选状态\"\n          value={statusFilter}\n          onChange={setStatusFilter}\n          style={{ width: 120 }}\n          allowClear\n        >\n          <Option value={InvitationStatus.PENDING}>待确认</Option>\n          <Option value={InvitationStatus.ACCEPTED}>已接受</Option>\n          <Option value={InvitationStatus.REJECTED}>已拒绝</Option>\n          <Option value={InvitationStatus.EXPIRED}>已过期</Option>\n          <Option value={InvitationStatus.CANCELLED}>已取消</Option>\n        </Select>\n      </Space>\n\n      {/* 邀请列表表格 */}\n      <Table\n        columns={columns}\n        dataSource={filteredInvitations}\n        rowKey=\"id\"\n        loading={loading}\n        pagination={{\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total) => `共 ${total} 条邀请记录`,\n          pageSize: 10,\n        }}\n      />\n    </Card>\n  );\n};\n\nexport default TeamInvitationList;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCC6Rb;;;2BAAA;;;;;;;oFAtR2C;yCAapC;0CAQA;mFAEW;6CAGgB;wCAED;8FACK;;;;;;;;;;YAEtC,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;YAC3B,MAAM,EAAE,MAAM,EAAE,GAAG,YAAM;YAMzB,MAAM,qBAAwD,CAAC,EAAE,SAAS,EAAE;;gBAC1E,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAA2B,EAAE;gBAC3E,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;gBAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAS;gBAEzD,SAAS;gBACT,MAAM,mBAAmB;oBACvB,IAAI;wBACF,WAAW;wBACX,MAAM,iBAAiB,MAAM,2BAAiB,CAAC,yBAAyB;wBACxE,eAAe,kBAAkB,EAAE;oBACrC,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,aAAO,CAAC,KAAK,CAAC;wBACd,eAAe,EAAE;oBACnB,SAAU;wBACR,WAAW;oBACb;gBACF;gBAEA,IAAA,gBAAS,EAAC;oBACR;gBACF,GAAG,EAAE;gBAEL,OAAO;gBACP,MAAM,yBAAyB,OAAO;oBACpC,IAAI;wBACF,MAAM,2BAAiB,CAAC,gBAAgB,CAAC;wBACzC,aAAO,CAAC,OAAO,CAAC;wBAChB;oBACA,6BAA6B;oBAC7B,iBAAiB;oBACnB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAEA,SAAS;gBACT,MAAM,sBAAsB,YAAY,MAAM,CAAC,CAAA;oBAC7C,MAAM,gBAAgB,CAAC,cACrB,WAAW,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACpE,WAAW,WAAW,IAAI,WAAW,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;oBAEjG,MAAM,gBAAgB,CAAC,gBAAgB,WAAW,MAAM,KAAK;oBAE7D,OAAO,iBAAiB;gBAC1B;gBAEA,QAAQ;gBACR,MAAM,UAA+C;oBACnD;wBACE,OAAO;wBACP,KAAK;wBACL,QAAQ,CAAC,GAAG,uBACV,2BAAC,WAAK;gCAAC,WAAU;gCAAW,MAAM;;kDAChC,2BAAC;wCAAK,MAAM;kDAAE,OAAO,WAAW,IAAI;;;;;;kDACpC,2BAAC;wCAAK,MAAK;wCAAY,OAAO;4CAAE,UAAU;wCAAO;;0DAC/C,2BAAC,mBAAY;;;;;4CAAG;4CAAE,OAAO,YAAY;;;;;;;;;;;;;oBAI7C;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,QAAQ,CAAC,QAAQ,uBACf,2BAAC,yBAAyB;gCACxB,QAAQ;gCACR,WAAW,OAAO,SAAS;;;;;;wBAG/B,SAAS;4BACP;gCAAE,MAAM;gCAAO,OAAO,qBAAgB,CAAC,OAAO;4BAAC;4BAC/C;gCAAE,MAAM;gCAAO,OAAO,qBAAgB,CAAC,QAAQ;4BAAC;4BAChD;gCAAE,MAAM;gCAAO,OAAO,qBAAgB,CAAC,QAAQ;4BAAC;4BAChD;gCAAE,MAAM;gCAAO,OAAO,qBAAgB,CAAC,OAAO;4BAAC;4BAC/C;gCAAE,MAAM;gCAAO,OAAO,qBAAgB,CAAC,SAAS;4BAAC;yBAClD;wBACD,UAAU,CAAC,OAAO,SAAW,OAAO,MAAM,KAAK;oBACjD;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,QAAQ,CAAC,qBACP,2BAAC,aAAO;gCAAC,OAAO,IAAA,cAAK,EAAC,MAAM,MAAM,CAAC;0CAChC,IAAA,cAAK,EAAC,MAAM,MAAM,CAAC;;;;;;wBAGxB,QAAQ,CAAC,GAAG,IAAM,IAAA,cAAK,EAAC,EAAE,SAAS,EAAE,IAAI,KAAK,IAAA,cAAK,EAAC,EAAE,SAAS,EAAE,IAAI;oBACvE;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,QAAQ,CAAC,MAAM;4BACb,MAAM,YAAY,OAAO,SAAS;4BAClC,qBACE,2BAAC,aAAO;gCAAC,OAAO,IAAA,cAAK,EAAC,MAAM,MAAM,CAAC;0CACjC,cAAA,2BAAC;oCAAK,MAAM,YAAY,WAAW;8CAChC,IAAA,cAAK,EAAC,MAAM,MAAM,CAAC;;;;;;;;;;;wBAI5B;oBACF;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,QAAQ,CAAC,OAAS,qBAChB,2BAAC,aAAO;gCAAC,OAAO,IAAA,cAAK,EAAC,MAAM,MAAM,CAAC;0CAChC,IAAA,cAAK,EAAC,MAAM,MAAM,CAAC;;;;;uCAEpB;oBACN;oBACA;wBACE,OAAO;wBACP,KAAK;wBACL,QAAQ,CAAC,GAAG;4BACV,IAAI,CAAC,OAAO,cAAc,EAAE,OAAO;4BAEnC,MAAM,WAAW;gCACf,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,cAAc,EAAG,IAAI,CAAC;oCACzD,aAAO,CAAC,OAAO,CAAC;gCAClB,GAAG,KAAK,CAAC;oCACP,aAAO,CAAC,KAAK,CAAC;gCAChB;4BACF;4BAEA,qBACE,2BAAC,WAAK;;kDACJ,2BAAC,aAAO;wCAAC,OAAM;kDACb,cAAA,2BAAC,YAAM;4CACL,MAAK;4CACL,oBAAM,2BAAC,mBAAY;;;;;4CACnB,SAAS;sDACV;;;;;;;;;;;kDAIH,2BAAC,aAAO;wCAAC,OAAO,OAAO,cAAc;kDACnC,cAAA,2BAAC,YAAM;4CACL,MAAK;4CACL,oBAAM,2BAAC,mBAAY;;;;;4CACnB,SAAS,IAAM,OAAO,IAAI,CAAC,OAAO,cAAc,EAAE;sDACnD;;;;;;;;;;;;;;;;;wBAMT;oBACF;oBACA;wBACE,OAAO;wBACP,KAAK;wBACL,QAAQ,CAAC,GAAG,uBACV,2BAAC,WAAK;0CACH,OAAO,cAAc,kBACpB,2BAAC,gBAAU;oCACT,OAAM;oCACN,aAAY;oCACZ,WAAW,IAAM,uBAAuB,OAAO,EAAE;oCACjD,QAAO;oCACP,YAAW;8CAEX,cAAA,2BAAC,YAAM;wCACL,MAAK;wCACL,MAAM;wCACN,oBAAM,2BAAC,qBAAc;;;;;kDACtB;;;;;;;;;;;;;;;;oBAOX;iBACD;gBAED,qBACE,2BAAC,UAAI;oBACH,OAAM;oBACN,qBACE,2BAAC,WAAK;kCACJ,cAAA,2BAAC,YAAM;4BACL,oBAAM,2BAAC,qBAAc;;;;;4BACrB,SAAS;4BACT,SAAS;sCACV;;;;;;;;;;;;sCAOL,2BAAC,WAAK;4BAAC,OAAO;gCAAE,cAAc;4BAAG;;8CAC/B,2BAAC,WAAK;oCACJ,aAAY;oCACZ,sBAAQ,2BAAC,qBAAc;;;;;oCACvB,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,OAAO;wCAAE,OAAO;oCAAI;;;;;;8CAEtB,2BAAC,YAAM;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU;oCACV,OAAO;wCAAE,OAAO;oCAAI;oCACpB,UAAU;;sDAEV,2BAAC;4CAAO,OAAO,qBAAgB,CAAC,OAAO;sDAAE;;;;;;sDACzC,2BAAC;4CAAO,OAAO,qBAAgB,CAAC,QAAQ;sDAAE;;;;;;sDAC1C,2BAAC;4CAAO,OAAO,qBAAgB,CAAC,QAAQ;sDAAE;;;;;;sDAC1C,2BAAC;4CAAO,OAAO,qBAAgB,CAAC,OAAO;sDAAE;;;;;;sDACzC,2BAAC;4CAAO,OAAO,qBAAgB,CAAC,SAAS;sDAAE;;;;;;;;;;;;;;;;;;sCAK/C,2BAAC,WAAK;4BACJ,SAAS;4BACT,YAAY;4BACZ,QAAO;4BACP,SAAS;4BACT,YAAY;gCACV,iBAAiB;gCACjB,iBAAiB;gCACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,MAAM,CAAC;gCACxC,UAAU;4BACZ;;;;;;;;;;;;YAIR;eA9OM;iBAAA;gBAgPN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;ID7RD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,wCAAuC;YAAC;YAAS;SAA8B;QAAC,kCAAiC;YAAC;YAAS;SAAwB;QAAC,wCAAuC;YAAC;YAAS;SAAuC;IAAA;;AACx+B"}