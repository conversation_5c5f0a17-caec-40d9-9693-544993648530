{"version": 3, "sources": ["src_pages_team-management_index_tsx-async.1047207444884190177.hot-update.js", "src/pages/team-management/components/TeamMemberManagement.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/team-management/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='10686901486861475193';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"common\",\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/invitations/index.tsx\":[\"common\",\"p__user__invitations__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"],\"src/pages/user/team-select/index.tsx\":[\"common\",\"src/pages/user/team-select/index.tsx\"]});;\r\n  },\r\n);\r\n", "/**\n * 团队成员管理组件\n * \n * 功能特性：\n * - 查看团队成员列表及详细信息\n * - 添加新成员（通过邮箱邀请）\n * - 移除团队现有成员\n * - 批量操作支持\n * - 成员搜索和筛选\n * \n * 权限控制：\n * - 只有团队创建者可以进行成员管理操作\n * - 创建者不能移除自己\n * - 提供详细的操作确认\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Modal,\n  Form,\n  message,\n  Tag,\n  Avatar,\n  Typography,\n  Popconfirm,\n  Select,\n  Divider,\n  Row,\n  Col,\n  Statistic\n} from 'antd';\nimport {\n  UserAddOutlined,\n  DeleteOutlined,\n  SearchOutlined,\n  MailOutlined,\n  UserOutlined,\n  CrownOutlined,\n  TeamOutlined\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\n\n// 导入服务和类型\nimport { TeamService } from '@/services/team';\nimport { InvitationService } from '@/services/invitation';\nimport type { TeamDetailResponse, TeamMemberResponse } from '@/types/api';\n\nconst { Text, Title } = Typography;\nconst { TextArea } = Input;\n\ninterface TeamMemberManagementProps {\n  teamDetail: TeamDetailResponse;\n  onRefresh: () => void;\n}\n\nconst TeamMemberManagement: React.FC<TeamMemberManagementProps> = ({\n  teamDetail,\n  onRefresh\n}) => {\n  const [loading, setLoading] = useState(false);\n  const [members, setMembers] = useState<TeamMemberResponse[]>([]);\n  const [searchText, setSearchText] = useState('');\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [inviteModalVisible, setInviteModalVisible] = useState(false);\n  const [inviteForm] = Form.useForm();\n\n  useEffect(() => {\n    fetchMembers();\n  }, []);\n\n  const fetchMembers = async () => {\n    try {\n      setLoading(true);\n      const memberList = await TeamService.getCurrentTeamMembers();\n      setMembers(memberList || []);\n    } catch (error) {\n      console.error('获取团队成员失败:', error);\n      message.error('获取团队成员失败');\n      setMembers([]); // 确保在错误时设置为空数组\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 邀请新成员\n  const handleInviteMembers = async (values: { emails: string; message?: string }) => {\n    try {\n      const emailList = values.emails\n        .split('\\n')\n        .map(email => email.trim())\n        .filter(email => email);\n\n      // 使用新的邀请链接功能\n      const response = await InvitationService.sendInvitations({\n        emails: emailList,\n        message: values.message\n      });\n\n      // 显示详细的发送结果\n      if (response.successCount > 0) {\n        message.success(`成功发送 ${response.successCount} 个邀请`);\n\n        // 显示邀请链接（可选：在开发环境中显示）\n        if (process.env.NODE_ENV === 'development') {\n          console.log('邀请链接:', response.invitations.map(inv => ({\n            email: inv.email,\n            link: inv.invitationLink\n          })));\n        }\n      }\n\n      if (response.failureCount > 0) {\n        message.warning(`${response.failureCount} 个邀请发送失败`);\n      }\n\n      setInviteModalVisible(false);\n      inviteForm.resetFields();\n      fetchMembers();\n      onRefresh();\n    } catch (error) {\n      console.error('邀请成员失败:', error);\n      message.error('邀请成员失败');\n    }\n  };\n\n  // 移除单个成员\n  const handleRemoveMember = async (member: TeamMemberResponse) => {\n    try {\n      await TeamService.removeMember(member.id);\n      message.success(`已移除成员：${member.name}`);\n      fetchMembers();\n      onRefresh();\n    } catch (error) {\n      console.error('移除成员失败:', error);\n      message.error('移除成员失败');\n    }\n  };\n\n  // 批量移除成员\n  const handleBatchRemove = async () => {\n    try {\n      const memberIds = selectedRowKeys as number[];\n      for (const memberId of memberIds) {\n        await TeamService.removeMember(memberId);\n      }\n      message.success(`已移除 ${memberIds.length} 名成员`);\n      setSelectedRowKeys([]);\n      fetchMembers();\n      onRefresh();\n    } catch (error) {\n      console.error('批量移除成员失败:', error);\n      message.error('批量移除成员失败');\n    }\n  };\n\n  // 筛选成员\n  const filteredMembers = (members || []).filter(member =>\n    member.name.toLowerCase().includes(searchText.toLowerCase()) ||\n    member.email.toLowerCase().includes(searchText.toLowerCase())\n  );\n\n  // 停用/启用成员\n  const handleToggleMemberStatus = async (member: TeamMemberResponse, isActive: boolean) => {\n    try {\n      await TeamService.updateMemberStatus(member.id, isActive);\n      message.success(`已${isActive ? '启用' : '停用'}成员：${member.name}`);\n      fetchMembers();\n      onRefresh();\n    } catch (error) {\n      console.error('更新成员状态失败:', error);\n      message.error('更新成员状态失败');\n    }\n  };\n\n  // 表格列配置\n  const columns: ColumnsType<TeamMemberResponse> = [\n    {\n      title: '姓名',\n      dataIndex: 'name',\n      key: 'name',\n      render: (name: string, record) => (\n        <Space>\n          <Text strong>{name}</Text>\n          {record.isCreator && (\n            <Tag icon={<CrownOutlined />} color=\"gold\">创建者</Tag>\n          )}\n        </Space>\n      ),\n    },\n    {\n      title: '邮箱',\n      dataIndex: 'email',\n      key: 'email',\n      render: (email: string) => (\n        <Text type=\"secondary\">{email}</Text>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'isActive',\n      key: 'status',\n      width: 100,\n      render: (isActive: boolean) => (\n        <Tag color={isActive ? 'green' : 'red'}>\n          {isActive ? '启用' : '停用'}\n        </Tag>\n      ),\n    },\n    {\n      title: '加入时间',\n      dataIndex: 'assignedAt',\n      key: 'assignedAt',\n      width: 150,\n      render: (date: string) => new Date(date).toLocaleDateString(),\n    },\n    {\n      title: '最后访问',\n      dataIndex: 'lastAccessTime',\n      key: 'lastAccessTime',\n      width: 150,\n      render: (date: string) => new Date(date).toLocaleDateString(),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      render: (_, record) => {\n        if (record.isCreator) {\n          return <Text type=\"secondary\">-</Text>;\n        }\n\n        return (\n          <Space>\n            {record.isActive ? (\n              <Button\n                type=\"text\"\n                size=\"small\"\n                onClick={() => handleToggleMemberStatus(record, false)}\n              >\n                停用\n              </Button>\n            ) : (\n              <Button\n                type=\"text\"\n                size=\"small\"\n                onClick={() => handleToggleMemberStatus(record, true)}\n              >\n                启用\n              </Button>\n            )}\n            <Popconfirm\n              title=\"确认移除成员\"\n              description={`确定要移除成员 ${record.name} 吗？此操作不可恢复。`}\n              onConfirm={() => handleRemoveMember(record)}\n              okText=\"确认\"\n              cancelText=\"取消\"\n              okType=\"danger\"\n            >\n              <Button\n                type=\"text\"\n                danger\n                size=\"small\"\n                icon={<DeleteOutlined />}\n              >\n                移除\n              </Button>\n            </Popconfirm>\n          </Space>\n        );\n      },\n    },\n  ];\n\n  // 行选择配置\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: setSelectedRowKeys,\n    getCheckboxProps: (record: TeamMemberResponse) => ({\n      disabled: record.isCreator, // 创建者不能被选择\n    }),\n  };\n\n  return (\n    <div>\n      {/* 统计信息 */}\n      <Row gutter={16} style={{ marginBottom: 24 }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"团队成员总数\"\n              value={(members || []).length}\n              prefix={<TeamOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"活跃成员\"\n              value={(members || []).filter(m => m.isActive).length}\n              prefix={<UserOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"待激活成员\"\n              value={(members || []).filter(m => !m.isActive).length}\n              prefix={<MailOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"管理员\"\n              value={(members || []).filter(m => m.isCreator).length}\n              prefix={<CrownOutlined />}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 操作栏 */}\n      <Card style={{ marginBottom: 16 }}>\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Space>\n              <Input\n                placeholder=\"搜索成员姓名或邮箱\"\n                prefix={<SearchOutlined />}\n                value={searchText}\n                onChange={(e) => setSearchText(e.target.value)}\n                style={{ width: 300 }}\n                allowClear\n              />\n              {selectedRowKeys.length > 0 && (\n                <Popconfirm\n                  title=\"批量移除成员\"\n                  description={`确定要移除选中的 ${selectedRowKeys.length} 名成员吗？此操作不可恢复。`}\n                  onConfirm={handleBatchRemove}\n                  okText=\"确认\"\n                  cancelText=\"取消\"\n                  okType=\"danger\"\n                >\n                  <Button\n                    danger\n                    icon={<DeleteOutlined />}\n                  >\n                    批量移除 ({selectedRowKeys.length})\n                  </Button>\n                </Popconfirm>\n              )}\n            </Space>\n          </Col>\n          <Col>\n            <Button\n              type=\"primary\"\n              icon={<UserAddOutlined />}\n              onClick={() => setInviteModalVisible(true)}\n            >\n              邀请成员\n            </Button>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 成员列表 */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={filteredMembers}\n          rowKey=\"id\"\n          loading={loading}\n          rowSelection={rowSelection}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 名成员`,\n            pageSize: 10,\n          }}\n        />\n      </Card>\n\n      {/* 邀请成员弹窗 */}\n      <Modal\n        title=\"邀请新成员\"\n        open={inviteModalVisible}\n        onCancel={() => {\n          setInviteModalVisible(false);\n          inviteForm.resetFields();\n        }}\n        footer={null}\n        width={600}\n      >\n        <Form\n          form={inviteForm}\n          layout=\"vertical\"\n          onFinish={handleInviteMembers}\n        >\n          <Form.Item\n            name=\"emails\"\n            label=\"邮箱地址\"\n            rules={[\n              { required: true, message: '请输入邮箱地址' },\n            ]}\n            extra=\"每行一个邮箱地址，支持批量邀请\"\n          >\n            <TextArea\n              rows={6}\n              placeholder=\"请输入邮箱地址，每行一个&#10;例如：&#10;<EMAIL>&#10;<EMAIL>\"\n            />\n          </Form.Item>\n          <Form.Item\n            name=\"message\"\n            label=\"邀请消息（可选）\"\n            extra=\"您可以添加一些邀请消息，让被邀请人更好地了解邀请意图\"\n          >\n            <TextArea\n              rows={3}\n              placeholder=\"欢迎加入我们的团队！我们期待与您一起工作...\"\n              maxLength={500}\n              showCount\n            />\n          </Form.Item>\n          <Form.Item>\n            <Space>\n              <Button type=\"primary\" htmlType=\"submit\" icon={<MailOutlined />}>\n                发送邀请\n              </Button>\n              <Button onClick={() => setInviteModalVisible(false)}>\n                取消\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default TeamMemberManagement;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCC4bb;;;2BAAA;;;;;;oFA/a2C;yCAmBpC;0CASA;yCAIqB;+CACM;;;;;;;;;;YAGlC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,gBAAU;YAClC,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAK;YAO1B,MAAM,uBAA4D,CAAC,EACjE,UAAU,EACV,SAAS,EACV;;gBACC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAuB,EAAE;gBAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;gBAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAc,EAAE;gBACtE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;gBAC7D,MAAM,CAAC,WAAW,GAAG,UAAI,CAAC,OAAO;gBAEjC,IAAA,gBAAS,EAAC;oBACR;gBACF,GAAG,EAAE;gBAEL,MAAM,eAAe;oBACnB,IAAI;wBACF,WAAW;wBACX,MAAM,aAAa,MAAM,iBAAW,CAAC,qBAAqB;wBAC1D,WAAW,cAAc,EAAE;oBAC7B,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,aAAO,CAAC,KAAK,CAAC;wBACd,WAAW,EAAE,GAAG,eAAe;oBACjC,SAAU;wBACR,WAAW;oBACb;gBACF;gBAEA,QAAQ;gBACR,MAAM,sBAAsB,OAAO;oBACjC,IAAI;wBACF,MAAM,YAAY,OAAO,MAAM,CAC5B,KAAK,CAAC,MACN,GAAG,CAAC,CAAA,QAAS,MAAM,IAAI,IACvB,MAAM,CAAC,CAAA,QAAS;wBAEnB,aAAa;wBACb,MAAM,WAAW,MAAM,6BAAiB,CAAC,eAAe,CAAC;4BACvD,QAAQ;4BACR,SAAS,OAAO,OAAO;wBACzB;wBAEA,YAAY;wBACZ,IAAI,SAAS,YAAY,GAAG,GAAG;4BAC7B,aAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,SAAS,YAAY,CAAC,IAAI,CAAC;4BAIjD,QAAQ,GAAG,CAAC,SAAS,SAAS,WAAW,CAAC,GAAG,CAAC,CAAA,MAAQ,CAAA;oCACpD,OAAO,IAAI,KAAK;oCAChB,MAAM,IAAI,cAAc;gCAC1B,CAAA;wBAEJ;wBAEA,IAAI,SAAS,YAAY,GAAG,GAC1B,aAAO,CAAC,OAAO,CAAC,CAAC,EAAE,SAAS,YAAY,CAAC,QAAQ,CAAC;wBAGpD,sBAAsB;wBACtB,WAAW,WAAW;wBACtB;wBACA;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAEA,SAAS;gBACT,MAAM,qBAAqB,OAAO;oBAChC,IAAI;wBACF,MAAM,iBAAW,CAAC,YAAY,CAAC,OAAO,EAAE;wBACxC,aAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,CAAC;wBACtC;wBACA;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAEA,SAAS;gBACT,MAAM,oBAAoB;oBACxB,IAAI;wBACF,MAAM,YAAY;wBAClB,KAAK,MAAM,YAAY,UACrB,MAAM,iBAAW,CAAC,YAAY,CAAC;wBAEjC,aAAO,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,UAAU,MAAM,CAAC,IAAI,CAAC;wBAC7C,mBAAmB,EAAE;wBACrB;wBACA;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAEA,OAAO;gBACP,MAAM,kBAAkB,AAAC,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM,CAAC,CAAA,SAC7C,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;gBAG5D,UAAU;gBACV,MAAM,2BAA2B,OAAO,QAA4B;oBAClE,IAAI;wBACF,MAAM,iBAAW,CAAC,kBAAkB,CAAC,OAAO,EAAE,EAAE;wBAChD,aAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,WAAW,OAAO,KAAK,GAAG,EAAE,OAAO,IAAI,CAAC,CAAC;wBAC7D;wBACA;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAEA,QAAQ;gBACR,MAAM,UAA2C;oBAC/C;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,QAAQ,CAAC,MAAc,uBACrB,2BAAC,WAAK;;kDACJ,2BAAC;wCAAK,MAAM;kDAAE;;;;;;oCACb,OAAO,SAAS,kBACf,2BAAC,SAAG;wCAAC,oBAAM,2BAAC,oBAAa;;;;;wCAAK,OAAM;kDAAO;;;;;;;;;;;;oBAInD;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,QAAQ,CAAC,sBACP,2BAAC;gCAAK,MAAK;0CAAa;;;;;;oBAE5B;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,OAAO;wBACP,QAAQ,CAAC,yBACP,2BAAC,SAAG;gCAAC,OAAO,WAAW,UAAU;0CAC9B,WAAW,OAAO;;;;;;oBAGzB;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,OAAO;wBACP,QAAQ,CAAC,OAAiB,IAAI,KAAK,MAAM,kBAAkB;oBAC7D;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,OAAO;wBACP,QAAQ,CAAC,OAAiB,IAAI,KAAK,MAAM,kBAAkB;oBAC7D;oBACA;wBACE,OAAO;wBACP,KAAK;wBACL,OAAO;wBACP,QAAQ,CAAC,GAAG;4BACV,IAAI,OAAO,SAAS,EAClB,qBAAO,2BAAC;gCAAK,MAAK;0CAAY;;;;;;4BAGhC,qBACE,2BAAC,WAAK;;oCACH,OAAO,QAAQ,iBACd,2BAAC,YAAM;wCACL,MAAK;wCACL,MAAK;wCACL,SAAS,IAAM,yBAAyB,QAAQ;kDACjD;;;;;6DAID,2BAAC,YAAM;wCACL,MAAK;wCACL,MAAK;wCACL,SAAS,IAAM,yBAAyB,QAAQ;kDACjD;;;;;;kDAIH,2BAAC,gBAAU;wCACT,OAAM;wCACN,aAAa,CAAC,QAAQ,EAAE,OAAO,IAAI,CAAC,WAAW,CAAC;wCAChD,WAAW,IAAM,mBAAmB;wCACpC,QAAO;wCACP,YAAW;wCACX,QAAO;kDAEP,cAAA,2BAAC,YAAM;4CACL,MAAK;4CACL,MAAM;4CACN,MAAK;4CACL,oBAAM,2BAAC,qBAAc;;;;;sDACtB;;;;;;;;;;;;;;;;;wBAMT;oBACF;iBACD;gBAED,QAAQ;gBACR,MAAM,eAAe;oBACnB;oBACA,UAAU;oBACV,kBAAkB,CAAC,SAAgC,CAAA;4BACjD,UAAU,OAAO,SAAS;wBAC5B,CAAA;gBACF;gBAEA,qBACE,2BAAC;;sCAEC,2BAAC,SAAG;4BAAC,QAAQ;4BAAI,OAAO;gCAAE,cAAc;4BAAG;;8CACzC,2BAAC,SAAG;oCAAC,MAAM;8CACT,cAAA,2BAAC,UAAI;kDACH,cAAA,2BAAC,eAAS;4CACR,OAAM;4CACN,OAAO,AAAC,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM;4CAC7B,sBAAQ,2BAAC,mBAAY;;;;;;;;;;;;;;;;;;;;8CAI3B,2BAAC,SAAG;oCAAC,MAAM;8CACT,cAAA,2BAAC,UAAI;kDACH,cAAA,2BAAC,eAAS;4CACR,OAAM;4CACN,OAAO,AAAC,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;4CACrD,sBAAQ,2BAAC,mBAAY;;;;;;;;;;;;;;;;;;;;8CAI3B,2BAAC,SAAG;oCAAC,MAAM;8CACT,cAAA,2BAAC,UAAI;kDACH,cAAA,2BAAC,eAAS;4CACR,OAAM;4CACN,OAAO,AAAC,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,QAAQ,EAAE,MAAM;4CACtD,sBAAQ,2BAAC,mBAAY;;;;;;;;;;;;;;;;;;;;8CAI3B,2BAAC,SAAG;oCAAC,MAAM;8CACT,cAAA,2BAAC,UAAI;kDACH,cAAA,2BAAC,eAAS;4CACR,OAAM;4CACN,OAAO,AAAC,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM;4CACtD,sBAAQ,2BAAC,oBAAa;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO9B,2BAAC,UAAI;4BAAC,OAAO;gCAAE,cAAc;4BAAG;sCAC9B,cAAA,2BAAC,SAAG;gCAAC,SAAQ;gCAAgB,OAAM;;kDACjC,2BAAC,SAAG;kDACF,cAAA,2BAAC,WAAK;;8DACJ,2BAAC,WAAK;oDACJ,aAAY;oDACZ,sBAAQ,2BAAC,qBAAc;;;;;oDACvB,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,OAAO;wDAAE,OAAO;oDAAI;oDACpB,UAAU;;;;;;gDAEX,gBAAgB,MAAM,GAAG,mBACxB,2BAAC,gBAAU;oDACT,OAAM;oDACN,aAAa,CAAC,SAAS,EAAE,gBAAgB,MAAM,CAAC,cAAc,CAAC;oDAC/D,WAAW;oDACX,QAAO;oDACP,YAAW;oDACX,QAAO;8DAEP,cAAA,2BAAC,YAAM;wDACL,MAAM;wDACN,oBAAM,2BAAC,qBAAc;;;;;;4DACtB;4DACQ,gBAAgB,MAAM;4DAAC;;;;;;;;;;;;;;;;;;;;;;;kDAMxC,2BAAC,SAAG;kDACF,cAAA,2BAAC,YAAM;4CACL,MAAK;4CACL,oBAAM,2BAAC,sBAAe;;;;;4CACtB,SAAS,IAAM,sBAAsB;sDACtC;;;;;;;;;;;;;;;;;;;;;;sCAQP,2BAAC,UAAI;sCACH,cAAA,2BAAC,WAAK;gCACJ,SAAS;gCACT,YAAY;gCACZ,QAAO;gCACP,SAAS;gCACT,cAAc;gCACd,YAAY;oCACV,iBAAiB;oCACjB,iBAAiB;oCACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;oCACtC,UAAU;gCACZ;;;;;;;;;;;sCAKJ,2BAAC,WAAK;4BACJ,OAAM;4BACN,MAAM;4BACN,UAAU;gCACR,sBAAsB;gCACtB,WAAW,WAAW;4BACxB;4BACA,QAAQ;4BACR,OAAO;sCAEP,cAAA,2BAAC,UAAI;gCACH,MAAM;gCACN,QAAO;gCACP,UAAU;;kDAEV,2BAAC,UAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAO;4CACL;gDAAE,UAAU;gDAAM,SAAS;4CAAU;yCACtC;wCACD,OAAM;kDAEN,cAAA,2BAAC;4CACC,MAAM;4CACN,aAAY;;;;;;;;;;;kDAGhB,2BAAC,UAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAM;kDAEN,cAAA,2BAAC;4CACC,MAAM;4CACN,aAAY;4CACZ,WAAW;4CACX,SAAS;;;;;;;;;;;kDAGb,2BAAC,UAAI,CAAC,IAAI;kDACR,cAAA,2BAAC,WAAK;;8DACJ,2BAAC,YAAM;oDAAC,MAAK;oDAAU,UAAS;oDAAS,oBAAM,2BAAC,mBAAY;;;;;8DAAK;;;;;;8DAGjE,2BAAC,YAAM;oDAAC,SAAS,IAAM,sBAAsB;8DAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASnE;eAjYM;;oBASiB,UAAI,CAAC;;;iBATtB;gBAmYN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;ID5bD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,wCAAuC;YAAC;YAAS;SAA8B;QAAC,kCAAiC;YAAC;YAAS;SAAwB;QAAC,wCAAuC;YAAC;YAAS;SAAuC;IAAA;;AACx+B"}