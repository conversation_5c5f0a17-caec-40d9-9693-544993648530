globalThis.makoModuleHotUpdate('src/pages/team-management/index.tsx', {
    modules: {
        "src/pages/team-management/components/TeamMemberManagement.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _team = __mako_require__("src/services/team.ts");
            var _invitation = __mako_require__("src/services/invitation.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Text, Title } = _antd.Typography;
            const { TextArea } = _antd.Input;
            const TeamMemberManagement = ({ teamDetail, onRefresh })=>{
                _s();
                const [loading, setLoading] = (0, _react.useState)(false);
                const [members, setMembers] = (0, _react.useState)([]);
                const [searchText, setSearchText] = (0, _react.useState)('');
                const [selectedRowKeys, setSelectedRowKeys] = (0, _react.useState)([]);
                const [inviteModalVisible, setInviteModalVisible] = (0, _react.useState)(false);
                const [inviteForm] = _antd.Form.useForm();
                (0, _react.useEffect)(()=>{
                    fetchMembers();
                }, []);
                const fetchMembers = async ()=>{
                    try {
                        setLoading(true);
                        const memberList = await _team.TeamService.getCurrentTeamMembers();
                        setMembers(memberList || []);
                    } catch (error) {
                        console.error('获取团队成员失败:', error);
                        _antd.message.error('获取团队成员失败');
                        setMembers([]); // 确保在错误时设置为空数组
                    } finally{
                        setLoading(false);
                    }
                };
                // 邀请新成员
                const handleInviteMembers = async (values)=>{
                    try {
                        const emailList = values.emails.split('\n').map((email)=>email.trim()).filter((email)=>email);
                        // 使用新的邀请链接功能
                        const response = await _invitation.InvitationService.sendInvitations({
                            emails: emailList,
                            message: values.message
                        });
                        // 显示详细的发送结果
                        if (response.successCount > 0) {
                            _antd.message.success(`成功发送 ${response.successCount} 个邀请`);
                            console.log('邀请链接:', response.invitations.map((inv)=>({
                                    email: inv.email,
                                    link: inv.invitationLink
                                })));
                        }
                        if (response.failureCount > 0) _antd.message.warning(`${response.failureCount} 个邀请发送失败`);
                        setInviteModalVisible(false);
                        inviteForm.resetFields();
                        // 只刷新成员列表，不刷新整个页面
                        fetchMembers();
                    // 移除 onRefresh() 调用，避免整个页面刷新
                    // onRefresh();
                    } catch (error) {
                        console.error('邀请成员失败:', error);
                        _antd.message.error('邀请成员失败');
                    }
                };
                // 移除单个成员
                const handleRemoveMember = async (member)=>{
                    try {
                        await _team.TeamService.removeMember(member.id);
                        _antd.message.success(`已移除成员：${member.name}`);
                        fetchMembers();
                    // 移除 onRefresh() 调用，避免整个页面刷新
                    // onRefresh();
                    } catch (error) {
                        console.error('移除成员失败:', error);
                        _antd.message.error('移除成员失败');
                    }
                };
                // 批量移除成员
                const handleBatchRemove = async ()=>{
                    try {
                        const memberIds = selectedRowKeys;
                        for (const memberId of memberIds)await _team.TeamService.removeMember(memberId);
                        _antd.message.success(`已移除 ${memberIds.length} 名成员`);
                        setSelectedRowKeys([]);
                        fetchMembers();
                    // 移除 onRefresh() 调用，避免整个页面刷新
                    // onRefresh();
                    } catch (error) {
                        console.error('批量移除成员失败:', error);
                        _antd.message.error('批量移除成员失败');
                    }
                };
                // 筛选成员
                const filteredMembers = (members || []).filter((member)=>member.name.toLowerCase().includes(searchText.toLowerCase()) || member.email.toLowerCase().includes(searchText.toLowerCase()));
                // 停用/启用成员
                const handleToggleMemberStatus = async (member, isActive)=>{
                    try {
                        await _team.TeamService.updateMemberStatus(member.id, isActive);
                        _antd.message.success(`已${isActive ? '启用' : '停用'}成员：${member.name}`);
                        fetchMembers();
                    // 移除 onRefresh() 调用，避免整个页面刷新
                    // onRefresh();
                    } catch (error) {
                        console.error('更新成员状态失败:', error);
                        _antd.message.error('更新成员状态失败');
                    }
                };
                // 表格列配置
                const columns = [
                    {
                        title: '姓名',
                        dataIndex: 'name',
                        key: 'name',
                        render: (name, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        children: name
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 193,
                                        columnNumber: 11
                                    }, this),
                                    record.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 195,
                                            columnNumber: 24
                                        }, void 0),
                                        color: "gold",
                                        children: "创建者"
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 195,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 192,
                                columnNumber: 9
                            }, this)
                    },
                    {
                        title: '邮箱',
                        dataIndex: 'email',
                        key: 'email',
                        render: (email)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                type: "secondary",
                                children: email
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 205,
                                columnNumber: 9
                            }, this)
                    },
                    {
                        title: '状态',
                        dataIndex: 'isActive',
                        key: 'status',
                        width: 100,
                        render: (isActive)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                color: isActive ? 'green' : 'red',
                                children: isActive ? '启用' : '停用'
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 214,
                                columnNumber: 9
                            }, this)
                    },
                    {
                        title: '加入时间',
                        dataIndex: 'assignedAt',
                        key: 'assignedAt',
                        width: 150,
                        render: (date)=>new Date(date).toLocaleDateString()
                    },
                    {
                        title: '最后访问',
                        dataIndex: 'lastAccessTime',
                        key: 'lastAccessTime',
                        width: 150,
                        render: (date)=>new Date(date).toLocaleDateString()
                    },
                    {
                        title: '操作',
                        key: 'action',
                        width: 200,
                        render: (_, record)=>{
                            if (record.isCreator) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                type: "secondary",
                                children: "-"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 239,
                                columnNumber: 18
                            }, this);
                            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    record.isActive ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "text",
                                        size: "small",
                                        onClick: ()=>handleToggleMemberStatus(record, false),
                                        children: "停用"
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 245,
                                        columnNumber: 15
                                    }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "text",
                                        size: "small",
                                        onClick: ()=>handleToggleMemberStatus(record, true),
                                        children: "启用"
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 253,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                                        title: "确认移除成员",
                                        description: `确定要移除成员 ${record.name} 吗？此操作不可恢复。`,
                                        onConfirm: ()=>handleRemoveMember(record),
                                        okText: "确认",
                                        cancelText: "取消",
                                        okType: "danger",
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "text",
                                            danger: true,
                                            size: "small",
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 273,
                                                columnNumber: 23
                                            }, void 0),
                                            children: "移除"
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 269,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 261,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 243,
                                columnNumber: 11
                            }, this);
                        }
                    }
                ];
                // 行选择配置
                const rowSelection = {
                    selectedRowKeys,
                    onChange: setSelectedRowKeys,
                    getCheckboxProps: (record)=>({
                            disabled: record.isCreator
                        })
                };
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                            gutter: 16,
                            style: {
                                marginBottom: 24
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    span: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                            title: "团队成员总数",
                                            value: (members || []).length,
                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 302,
                                                columnNumber: 23
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 299,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 298,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 297,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    span: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                            title: "活跃成员",
                                            value: (members || []).filter((m)=>m.isActive).length,
                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 311,
                                                columnNumber: 23
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 308,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 307,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 306,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    span: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                            title: "待激活成员",
                                            value: (members || []).filter((m)=>!m.isActive).length,
                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 320,
                                                columnNumber: 23
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 317,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 316,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 315,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    span: 6,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Statistic, {
                                            title: "管理员",
                                            value: (members || []).filter((m)=>m.isCreator).length,
                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 329,
                                                columnNumber: 23
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 326,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 325,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 324,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 296,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            style: {
                                marginBottom: 16
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                justify: "space-between",
                                align: "middle",
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                    placeholder: "搜索成员姓名或邮箱",
                                                    prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                        lineNumber: 342,
                                                        columnNumber: 25
                                                    }, void 0),
                                                    value: searchText,
                                                    onChange: (e)=>setSearchText(e.target.value),
                                                    style: {
                                                        width: 300
                                                    },
                                                    allowClear: true
                                                }, void 0, false, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 340,
                                                    columnNumber: 15
                                                }, this),
                                                selectedRowKeys.length > 0 && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                                                    title: "批量移除成员",
                                                    description: `确定要移除选中的 ${selectedRowKeys.length} 名成员吗？此操作不可恢复。`,
                                                    onConfirm: handleBatchRemove,
                                                    okText: "确认",
                                                    cancelText: "取消",
                                                    okType: "danger",
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                        danger: true,
                                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                            lineNumber: 359,
                                                            columnNumber: 27
                                                        }, void 0),
                                                        children: [
                                                            "批量移除 (",
                                                            selectedRowKeys.length,
                                                            ")"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                        lineNumber: 357,
                                                        columnNumber: 19
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 349,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 339,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 338,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "primary",
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserAddOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 370,
                                                columnNumber: 21
                                            }, void 0),
                                            onClick: ()=>setInviteModalVisible(true),
                                            children: "邀请成员"
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 368,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 367,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 337,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 336,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Table, {
                                columns: columns,
                                dataSource: filteredMembers,
                                rowKey: "id",
                                loading: loading,
                                rowSelection: rowSelection,
                                pagination: {
                                    showSizeChanger: true,
                                    showQuickJumper: true,
                                    showTotal: (total)=>`共 ${total} 名成员`,
                                    pageSize: 10
                                }
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 381,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 380,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                            title: "邀请新成员",
                            open: inviteModalVisible,
                            onCancel: ()=>{
                                setInviteModalVisible(false);
                                inviteForm.resetFields();
                            },
                            footer: null,
                            width: 600,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                                form: inviteForm,
                                layout: "vertical",
                                onFinish: handleInviteMembers,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        name: "emails",
                                        label: "邮箱地址",
                                        rules: [
                                            {
                                                required: true,
                                                message: '请输入邮箱地址'
                                            }
                                        ],
                                        extra: "每行一个邮箱地址，支持批量邀请",
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                            rows: 6,
                                            placeholder: "请输入邮箱地址，每行一个 例如： <EMAIL> <EMAIL>"
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 420,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 412,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        name: "message",
                                        label: "邀请消息（可选）",
                                        extra: "您可以添加一些邀请消息，让被邀请人更好地了解邀请意图",
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                            rows: 3,
                                            placeholder: "欢迎加入我们的团队！我们期待与您一起工作...",
                                            maxLength: 500,
                                            showCount: true
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 430,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 425,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    type: "primary",
                                                    htmlType: "submit",
                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                        lineNumber: 439,
                                                        columnNumber: 62
                                                    }, void 0),
                                                    children: "发送邀请"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 439,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    onClick: ()=>setInviteModalVisible(false),
                                                    children: "取消"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 442,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 438,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 437,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 407,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 397,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                    lineNumber: 294,
                    columnNumber: 5
                }, this);
            };
            _s(TeamMemberManagement, "lwyw8TlHxmNVMk/bokFh1nGrhdE=", false, function() {
                return [
                    _antd.Form.useForm
                ];
            });
            _c = TeamMemberManagement;
            var _default = TeamMemberManagement;
            var _c;
            $RefreshReg$(_c, "TeamMemberManagement");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '8347278114515712687';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "common",
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/invitations/index.tsx": [
            "common",
            "p__user__invitations__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ],
        "src/pages/user/team-select/index.tsx": [
            "common",
            "src/pages/user/team-select/index.tsx"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_team-management_index_tsx-async.14313018211565307457.hot-update.js.map