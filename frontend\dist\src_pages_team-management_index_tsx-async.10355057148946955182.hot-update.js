globalThis.makoModuleHotUpdate('src/pages/team-management/index.tsx', {
    modules: {
        "src/pages/team-management/index.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _TeamMemberManagement = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team-management/components/TeamMemberManagement.tsx"));
            var _TeamSettings = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team-management/components/TeamSettings.tsx"));
            var _TeamInvitationList = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team-management/components/TeamInvitationList.tsx"));
            var _team = __mako_require__("src/services/team.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Title, Text } = _antd.Typography;
            const TeamManagementPage = ()=>{
                _s();
                const [loading, setLoading] = (0, _react.useState)(true);
                const [teamDetail, setTeamDetail] = (0, _react.useState)(null);
                const [activeTab, setActiveTab] = (0, _react.useState)('members');
                (0, _react.useEffect)(()=>{
                    fetchTeamDetail();
                }, []);
                const fetchTeamDetail = async ()=>{
                    try {
                        setLoading(true);
                        const detail = await _team.TeamService.getCurrentTeamDetail();
                        setTeamDetail(detail);
                    } catch (error) {
                        console.error('获取团队详情失败:', error);
                        // 如果获取失败，可能是没有选择团队，跳转到团队选择页面
                        _max.history.push('/user/team-select');
                    } finally{
                        setLoading(false);
                    }
                };
                // 智能刷新函数：只在必要时刷新团队详情
                const handleSmartRefresh = async (forceRefresh = false)=>{
                    // 只有在强制刷新或者团队设置可能发生变化时才刷新
                    if (forceRefresh || activeTab === 'settings') await fetchTeamDetail();
                // 对于成员管理和邀请管理，子组件会自己处理数据刷新
                };
                // 权限检查：只有团队创建者可以访问管理功能
                const hasManagePermission = (teamDetail === null || teamDetail === void 0 ? void 0 : teamDetail.isCreator) || false;
                if (loading) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            textAlign: 'center',
                            padding: '50px 0'
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                                size: "large"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/index.tsx",
                                lineNumber: 87,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    marginTop: 16
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: "secondary",
                                    children: "正在加载团队信息..."
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 89,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/team-management/index.tsx",
                                lineNumber: 88,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 86,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team-management/index.tsx",
                    lineNumber: 85,
                    columnNumber: 7
                }, this);
                if (!teamDetail) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                textAlign: 'center',
                                padding: '50px 0'
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.InfoCircleOutlined, {
                                    style: {
                                        fontSize: 48,
                                        color: '#faad14',
                                        marginBottom: 16
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 101,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                    level: 4,
                                    children: "未找到团队信息"
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 102,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: "secondary",
                                    children: "请先选择一个团队"
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 103,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginTop: 16
                                    },
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        onClick: ()=>_max.history.push('/user/team-select'),
                                        children: "选择团队"
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/index.tsx",
                                        lineNumber: 105,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 104,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 100,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 99,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team-management/index.tsx",
                    lineNumber: 98,
                    columnNumber: 7
                }, this);
                // 权限不足提示
                if (!hasManagePermission) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                            message: "权限不足",
                            description: "只有团队创建者可以访问团队管理功能。如果您需要管理权限，请联系团队创建者。",
                            type: "warning",
                            showIcon: true,
                            action: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                size: "small",
                                onClick: ()=>_max.history.push('/dashboard'),
                                children: "返回首页"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/index.tsx",
                                lineNumber: 126,
                                columnNumber: 15
                            }, void 0)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 120,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 119,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team-management/index.tsx",
                    lineNumber: 118,
                    columnNumber: 7
                }, this);
                // 选项卡配置
                const tabItems = [
                    {
                        key: 'members',
                        label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 142,
                                    columnNumber: 11
                                }, this),
                                "团队成员管理"
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 141,
                            columnNumber: 9
                        }, this),
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamMemberManagement.default, {
                            teamDetail: teamDetail,
                            onRefresh: ()=>handleSmartRefresh(false)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 147,
                            columnNumber: 9
                        }, this)
                    },
                    {
                        key: 'invitations',
                        label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 157,
                                    columnNumber: 11
                                }, this),
                                "邀请管理"
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 156,
                            columnNumber: 9
                        }, this),
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamInvitationList.default, {
                            onRefresh: ()=>handleSmartRefresh(false)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 162,
                            columnNumber: 9
                        }, this)
                    },
                    {
                        key: 'settings',
                        label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 171,
                                    columnNumber: 11
                                }, this),
                                "团队设置"
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 170,
                            columnNumber: 9
                        }, this),
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamSettings.default, {
                            teamDetail: teamDetail,
                            onRefresh: fetchTeamDetail
                        }, void 0, false, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 176,
                            columnNumber: 9
                        }, this)
                    }
                ];
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tabs, {
                            activeKey: activeTab,
                            onChange: setActiveTab,
                            items: tabItems,
                            size: "large",
                            tabBarStyle: {
                                marginBottom: 24
                            }
                        }, void 0, false, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 187,
                            columnNumber: 9
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 186,
                        columnNumber: 7
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team-management/index.tsx",
                    lineNumber: 185,
                    columnNumber: 5
                }, this);
            };
            _s(TeamManagementPage, "MbpIoVQJb3zY91BHiEpQzi7JPmo=");
            _c = TeamManagementPage;
            var _default = TeamManagementPage;
            var _c;
            $RefreshReg$(_c, "TeamManagementPage");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '13938161196296927631';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "common",
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/invitations/index.tsx": [
            "common",
            "p__user__invitations__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ],
        "src/pages/user/team-select/index.tsx": [
            "common",
            "src/pages/user/team-select/index.tsx"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_team-management_index_tsx-async.10355057148946955182.hot-update.js.map