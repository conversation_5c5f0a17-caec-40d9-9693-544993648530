{"version": 3, "sources": ["src_pages_team-management_index_tsx-async.10355057148946955182.hot-update.js", "src/pages/team-management/index.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/team-management/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='13938161196296927631';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"common\",\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/invitations/index.tsx\":[\"common\",\"p__user__invitations__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"],\"src/pages/user/team-select/index.tsx\":[\"common\",\"src/pages/user/team-select/index.tsx\"]});;\r\n  },\r\n);\r\n", "/**\n * 集成团队管理页面\n * \n * 功能特性：\n * - 统一的团队管理界面，包含多个功能模块\n * - 选项卡布局，便于在不同管理功能之间切换\n * - 团队成员管理、账户管理、团队设置等功能\n * - 权限控制，只有团队创建者可以访问管理功能\n * \n * 模块组织：\n * - 团队成员管理：查看、添加、移除团队成员\n * - 成员账户管理：管理成员权限和角色\n * - 团队设置：编辑团队信息、删除团队\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { \n  Tabs, \n  Card, \n  Alert, \n  Spin, \n  Typography, \n  Space,\n  Tag,\n  Avatar,\n  Button\n} from 'antd';\nimport {\n  UserOutlined,\n  SettingOutlined,\n  MailOutlined,\n  InfoCircleOutlined\n} from '@ant-design/icons';\nimport { history } from '@umijs/max';\n\n// 导入子组件\nimport TeamMemberManagement from './components/TeamMemberManagement';\nimport TeamSettings from './components/TeamSettings';\nimport TeamInvitationList from './components/TeamInvitationList';\n\n// 导入服务和类型\nimport { TeamService } from '@/services/team';\nimport type { TeamDetailResponse } from '@/types/api';\n\nconst { Title, Text } = Typography;\n\nconst TeamManagementPage: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [teamDetail, setTeamDetail] = useState<TeamDetailResponse | null>(null);\n  const [activeTab, setActiveTab] = useState('members');\n\n  useEffect(() => {\n    fetchTeamDetail();\n  }, []);\n\n  const fetchTeamDetail = async () => {\n    try {\n      setLoading(true);\n      const detail = await TeamService.getCurrentTeamDetail();\n      setTeamDetail(detail);\n    } catch (error) {\n      console.error('获取团队详情失败:', error);\n      // 如果获取失败，可能是没有选择团队，跳转到团队选择页面\n      history.push('/user/team-select');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 智能刷新函数：只在必要时刷新团队详情\n  const handleSmartRefresh = async (forceRefresh = false) => {\n    // 只有在强制刷新或者团队设置可能发生变化时才刷新\n    if (forceRefresh || activeTab === 'settings') {\n      await fetchTeamDetail();\n    }\n    // 对于成员管理和邀请管理，子组件会自己处理数据刷新\n  };\n\n  // 权限检查：只有团队创建者可以访问管理功能\n  const hasManagePermission = teamDetail?.isCreator || false;\n\n  if (loading) {\n    return (\n      <PageContainer>\n        <div style={{ textAlign: 'center', padding: '50px 0' }}>\n          <Spin size=\"large\" />\n          <div style={{ marginTop: 16 }}>\n            <Text type=\"secondary\">正在加载团队信息...</Text>\n          </div>\n        </div>\n      </PageContainer>\n    );\n  }\n\n  if (!teamDetail) {\n    return (\n      <PageContainer>\n        <Card>\n          <div style={{ textAlign: 'center', padding: '50px 0' }}>\n            <InfoCircleOutlined style={{ fontSize: 48, color: '#faad14', marginBottom: 16 }} />\n            <Title level={4}>未找到团队信息</Title>\n            <Text type=\"secondary\">请先选择一个团队</Text>\n            <div style={{ marginTop: 16 }}>\n              <Button type=\"primary\" onClick={() => history.push('/user/team-select')}>\n                选择团队\n              </Button>\n            </div>\n          </div>\n        </Card>\n      </PageContainer>\n    );\n  }\n\n  // 权限不足提示\n  if (!hasManagePermission) {\n    return (\n      <PageContainer>\n        <Card>\n          <Alert\n            message=\"权限不足\"\n            description=\"只有团队创建者可以访问团队管理功能。如果您需要管理权限，请联系团队创建者。\"\n            type=\"warning\"\n            showIcon\n            action={\n              <Button size=\"small\" onClick={() => history.push('/dashboard')}>\n                返回首页\n              </Button>\n            }\n          />\n        </Card>\n      </PageContainer>\n    );\n  }\n\n  // 选项卡配置\n  const tabItems = [\n    {\n      key: 'members',\n      label: (\n        <Space>\n          <UserOutlined />\n          团队成员管理\n        </Space>\n      ),\n      children: (\n        <TeamMemberManagement\n          teamDetail={teamDetail}\n          onRefresh={() => handleSmartRefresh(false)}\n        />\n      ),\n    },\n    {\n      key: 'invitations',\n      label: (\n        <Space>\n          <MailOutlined />\n          邀请管理\n        </Space>\n      ),\n      children: (\n        <TeamInvitationList\n          onRefresh={() => handleSmartRefresh(false)}\n        />\n      ),\n    },\n    {\n      key: 'settings',\n      label: (\n        <Space>\n          <SettingOutlined />\n          团队设置\n        </Space>\n      ),\n      children: (\n        <TeamSettings\n          teamDetail={teamDetail}\n          onRefresh={fetchTeamDetail}\n        />\n      ),\n    },\n  ];\n\n  return (\n    <PageContainer>\n      <Card>\n        <Tabs\n          activeKey={activeTab}\n          onChange={setActiveTab}\n          items={tabItems}\n          size=\"large\"\n          tabBarStyle={{ marginBottom: 24 }}\n        />\n      </Card>\n    </PageContainer>\n  );\n};\n\nexport default TeamManagementPage;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCCmMb;;;2BAAA;;;;;;;oFAvL2C;kDACb;yCAWvB;0CAMA;wCACiB;kGAGS;0FACR;gGACM;yCAGH;;;;;;;;;;YAG5B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;YAElC,MAAM,qBAA+B;;gBACnC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAA4B;gBACxE,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAC;gBAE3C,IAAA,gBAAS,EAAC;oBACR;gBACF,GAAG,EAAE;gBAEL,MAAM,kBAAkB;oBACtB,IAAI;wBACF,WAAW;wBACX,MAAM,SAAS,MAAM,iBAAW,CAAC,oBAAoB;wBACrD,cAAc;oBAChB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,6BAA6B;wBAC7B,YAAO,CAAC,IAAI,CAAC;oBACf,SAAU;wBACR,WAAW;oBACb;gBACF;gBAEA,qBAAqB;gBACrB,MAAM,qBAAqB,OAAO,eAAe,KAAK;oBACpD,0BAA0B;oBAC1B,IAAI,gBAAgB,cAAc,YAChC,MAAM;gBAER,2BAA2B;gBAC7B;gBAEA,uBAAuB;gBACvB,MAAM,sBAAsB,CAAA,uBAAA,iCAAA,WAAY,SAAS,KAAI;gBAErD,IAAI,SACF,qBACE,2BAAC,4BAAa;8BACZ,cAAA,2BAAC;wBAAI,OAAO;4BAAE,WAAW;4BAAU,SAAS;wBAAS;;0CACnD,2BAAC,UAAI;gCAAC,MAAK;;;;;;0CACX,2BAAC;gCAAI,OAAO;oCAAE,WAAW;gCAAG;0CAC1B,cAAA,2BAAC;oCAAK,MAAK;8CAAY;;;;;;;;;;;;;;;;;;;;;;gBAOjC,IAAI,CAAC,YACH,qBACE,2BAAC,4BAAa;8BACZ,cAAA,2BAAC,UAAI;kCACH,cAAA,2BAAC;4BAAI,OAAO;gCAAE,WAAW;gCAAU,SAAS;4BAAS;;8CACnD,2BAAC,yBAAkB;oCAAC,OAAO;wCAAE,UAAU;wCAAI,OAAO;wCAAW,cAAc;oCAAG;;;;;;8CAC9E,2BAAC;oCAAM,OAAO;8CAAG;;;;;;8CACjB,2BAAC;oCAAK,MAAK;8CAAY;;;;;;8CACvB,2BAAC;oCAAI,OAAO;wCAAE,WAAW;oCAAG;8CAC1B,cAAA,2BAAC,YAAM;wCAAC,MAAK;wCAAU,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;kDAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAUrF,SAAS;gBACT,IAAI,CAAC,qBACH,qBACE,2BAAC,4BAAa;8BACZ,cAAA,2BAAC,UAAI;kCACH,cAAA,2BAAC,WAAK;4BACJ,SAAQ;4BACR,aAAY;4BACZ,MAAK;4BACL,QAAQ;4BACR,sBACE,2BAAC,YAAM;gCAAC,MAAK;gCAAQ,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;0CAAe;;;;;;;;;;;;;;;;;;;;;gBAU5E,QAAQ;gBACR,MAAM,WAAW;oBACf;wBACE,KAAK;wBACL,qBACE,2BAAC,WAAK;;8CACJ,2BAAC,mBAAY;;;;;gCAAG;;;;;;;wBAIpB,wBACE,2BAAC,6BAAoB;4BACnB,YAAY;4BACZ,WAAW,IAAM,mBAAmB;;;;;;oBAG1C;oBACA;wBACE,KAAK;wBACL,qBACE,2BAAC,WAAK;;8CACJ,2BAAC,mBAAY;;;;;gCAAG;;;;;;;wBAIpB,wBACE,2BAAC,2BAAkB;4BACjB,WAAW,IAAM,mBAAmB;;;;;;oBAG1C;oBACA;wBACE,KAAK;wBACL,qBACE,2BAAC,WAAK;;8CACJ,2BAAC,sBAAe;;;;;gCAAG;;;;;;;wBAIvB,wBACE,2BAAC,qBAAY;4BACX,YAAY;4BACZ,WAAW;;;;;;oBAGjB;iBACD;gBAED,qBACE,2BAAC,4BAAa;8BACZ,cAAA,2BAAC,UAAI;kCACH,cAAA,2BAAC,UAAI;4BACH,WAAW;4BACX,UAAU;4BACV,OAAO;4BACP,MAAK;4BACL,aAAa;gCAAE,cAAc;4BAAG;;;;;;;;;;;;;;;;YAK1C;eArJM;iBAAA;gBAuJN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDnMD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,wCAAuC;YAAC;YAAS;SAA8B;QAAC,kCAAiC;YAAC;YAAS;SAAwB;QAAC,wCAAuC;YAAC;YAAS;SAAuC;IAAA;;AACx+B"}